import React, { useState } from 'react';
import { Card, Tabs, message } from 'antd';
import CanvasRenderer from './components/CanvasRenderer';
import { getMetricsRelationshipList, getDimensionRelationshipList } from '../../services/system';

// 定义组件Props类型
interface RootCauseAnalysisProps {
  agentId?: number;
}

// 定义数据项类型
interface RelationshipItem {
  datasetId: number;
  filed: string;
  id: number;
  pid: number;
  relationName: string;
  type: string;
}

const RootCauseAnalysis: React.FC<RootCauseAnalysisProps> = ({ agentId }) => {
  const [activeKey, setActiveKey] = useState('dimension');

  // 获取真实数据
  const fetchCanvasData = async (type: 'dimension' | 'metric'): Promise<RelationshipItem[]> => {
    if (!agentId) {
      message.warning('缺少agentId参数');
      return [];
    }

    try {
      let response;
      if (type === 'dimension') {
        response = await getDimensionRelationshipList(agentId);
      } else {
        response = await getMetricsRelationshipList(agentId);
      }

      if (response.code === 200 && response.data) {
        return response.data;
      } else {
        message.error(`获取${type === 'dimension' ? '维度' : '指标'}关系失败`);
        return [];
      }
    } catch (error) {
      console.error(`获取${type === 'dimension' ? '维度' : '指标'}数据失败:`, error);
      message.error(`获取${type === 'dimension' ? '维度' : '指标'}数据失败`);
      return [];
    }
  };

  const tabItems = [
    {
      key: 'dimension',
      label: '维度',
      children: (
        <CanvasRenderer
          dataType="dimension"
          fetchData={() => fetchCanvasData('dimension')}
        />
      ),
    },
    {
      key: 'metric',
      label: '指标',
      children: (
        <CanvasRenderer
          dataType="metric"
          fetchData={() => fetchCanvasData('metric')}
        />
      ),
    },
  ];

  const handleNodeClick = (node: any) => {
    console.log('节点点击:', node);
    // 这里可以添加节点点击的处理逻辑，比如显示详情、编辑等
  };

  const handleEdgeClick = (edge: any) => {
    console.log('边点击:', edge);
    // 这里可以添加边点击的处理逻辑，比如编辑关系等
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card
        title="归因分析"
        extra={
          <div style={{ fontSize: '12px', color: '#666' }}>
            {activeKey === 'dimension' ? '维度关系图' : '指标关系图'}
          </div>
        }
      >
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          items={tabItems.map(item => ({
            ...item,
            children: React.cloneElement(item.children as React.ReactElement, {
              onNodeClick: handleNodeClick,
              onEdgeClick: handleEdgeClick,
            }),
          }))}
          type="card"
          tabBarStyle={{ marginBottom: '16px' }}
        />
      </Card>
    </div>
  );
};

export default RootCauseAnalysis;
