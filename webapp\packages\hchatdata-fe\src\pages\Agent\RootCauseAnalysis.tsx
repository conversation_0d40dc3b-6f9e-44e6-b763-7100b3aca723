import React, { useState } from 'react';
import { Card, Tabs } from 'antd';
import CanvasRenderer from './components/CanvasRenderer';

// Mock数据模拟接口返回
const mockDimensionData = [
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.corporate_balance",
    "id": 1,
    "pid": 0,
    "relationName": "企业资产负债",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.total_assets",
    "id": 2,
    "pid": 1,
    "relationName": "总资产",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.current_assets",
    "id": 3,
    "pid": 2,
    "relationName": "流动资产",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.fixed_assets",
    "id": 4,
    "pid": 2,
    "relationName": "固定资产",
    "type": "component"
  }
];

const mockMetricData = [
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.corporate_balance",
    "id": 9,
    "pid": 0,
    "relationName": "贷款余额",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.corporate_loan_balance",
    "id": 10,
    "pid": 9,
    "relationName": "对公贷款余额",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.retail_loan_balance",
    "id": 11,
    "pid": 9,
    "relationName": "零售贷款余额",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.loan_loss_provision",
    "id": 12,
    "pid": 9,
    "relationName": "贷款损失准备",
    "type": "component"
  },
  {
    "datasetId": 24,
    "filed": "bs_fact_business_metrics.npl_ratio",
    "id": 14,
    "pid": 12,
    "relationName": "不良贷款率",
    "type": "calculated"
  }
];

const RootCauseAnalysis: React.FC = () => {
  const [activeKey, setActiveKey] = useState('dimension');

  // 模拟异步数据获取
  const fetchCanvasData = async (type: 'dimension' | 'metric') => {
    // 模拟网络延迟
    await new Promise(resolve => setTimeout(resolve, 500));

    return type === 'dimension' ? mockDimensionData : mockMetricData;
  };

  const tabItems = [
    {
      key: 'dimension',
      label: '维度',
      children: (
        <CanvasRenderer
          dataType="dimension"
          fetchData={() => fetchCanvasData('dimension')}
        />
      ),
    },
    {
      key: 'metric',
      label: '指标',
      children: (
        <CanvasRenderer
          dataType="metric"
          fetchData={() => fetchCanvasData('metric')}
        />
      ),
    },
  ];

  const handleNodeClick = (node: any) => {
    console.log('节点点击:', node);
    // 这里可以添加节点点击的处理逻辑，比如显示详情、编辑等
  };

  const handleEdgeClick = (edge: any) => {
    console.log('边点击:', edge);
    // 这里可以添加边点击的处理逻辑，比如编辑关系等
  };

  return (
    <div style={{ padding: '16px' }}>
      <Card
        title="归因分析"
        extra={
          <div style={{ fontSize: '12px', color: '#666' }}>
            {activeKey === 'dimension' ? '维度关系图' : '指标关系图'}
          </div>
        }
      >
        <Tabs
          activeKey={activeKey}
          onChange={setActiveKey}
          items={tabItems.map(item => ({
            ...item,
            children: React.cloneElement(item.children as React.ReactElement, {
              onNodeClick: handleNodeClick,
              onEdgeClick: handleEdgeClick,
            }),
          }))}
          type="card"
          tabBarStyle={{ marginBottom: '16px' }}
        />
      </Card>
    </div>
  );
};

export default RootCauseAnalysis;
