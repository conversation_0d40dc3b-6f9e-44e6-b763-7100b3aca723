import React, { useEffect, useRef, useState } from 'react';
import { Spin, message, Empty } from 'antd';
import G6, { Graph, GraphData } from '@antv/g6';

interface RawDataItem {
  datasetId: number;
  filed: string;
  id: number;
  pid: number;
  relationName: string;
  type: 'component' | 'calculated';
}

interface CanvasRendererProps {
  dataType: 'dimension' | 'metric';
  fetchData: () => Promise<RawDataItem[]>;
  onNodeClick?: (node: any) => void;
  onEdgeClick?: (edge: any) => void;
}

const CanvasRenderer: React.FC<CanvasRendererProps> = ({
  dataType,
  fetchData,
  onNodeClick,
  onEdgeClick,
}) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const graphRef = useRef<Graph | null>(null);
  const [loading, setLoading] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const [isEmpty, setIsEmpty] = useState(false);

  // 将后端数据转换为G6格式
  const transformDataToG6Format = (rawData: RawDataItem[]): GraphData => {
    // 数据验证：确保所有节点的父节点都存在
    const nodeIds = new Set(rawData.map(item => item.id));
    const validatedData = rawData.filter(item => {
      if (item.pid === 0) return true; // 根节点
      if (!nodeIds.has(item.pid)) {
        console.warn(`节点 ${item.id}(${item.relationName}) 的父节点 ${item.pid} 不存在，已过滤`);
        return false;
      }
      return true;
    });

    const nodes = validatedData.map(item => ({
      id: item.id.toString(),
      label: item.relationName,
      type: 'component-node', // 统一使用矩形节点
      style: {
        fill: '#69c0ff',
        stroke: '#1890ff',
        lineWidth: 2,
      },
      labelCfg: {
        style: {
          fill: '#000',
          fontSize: 12,
          fontWeight: 'bold' as const,
        },
      },
      // 保存原始数据用于后续扩展
      originalData: item,
    }));

    const edges = validatedData
      .filter(item => item.pid !== 0)
      .map(item => ({
        id: `edge-${item.pid}-${item.id}`,
        source: item.pid.toString(),
        target: item.id.toString(),
        style: {
          stroke: '#666',
          lineWidth: 2,
          endArrow: {
            path: G6.Arrow.triangle(8, 8, 0),
            fill: '#666',
          },
        },
      }));

    return { nodes, edges };
  };

  // 初始化G6图
  const initGraph = () => {
    if (!containerRef.current) return;

    // 清理之前的图实例
    if (graphRef.current) {
      graphRef.current.destroy();
    }

    const width = containerRef.current.offsetWidth;
    const height = 600;

    // 注册自定义节点类型
    G6.registerNode('component-node', {
      draw(cfg, group) {
        const rect = group!.addShape('rect', {
          attrs: {
            x: -60,
            y: -25,
            width: 120,
            height: 50,
            fill: cfg?.style?.fill || '#69c0ff',
            stroke: cfg?.style?.stroke || '#1890ff',
            lineWidth: cfg?.style?.lineWidth || 2,
            radius: 6,
          },
          name: 'rect-shape',
        });

        if (cfg?.label) {
          // 处理长文本换行
          const text = cfg.label as string;
          const fontSize = 12;

          if (text.length > 8) {
            // 如果文本太长，分两行显示
            const firstLine = text.substring(0, 6);
            const secondLine = text.substring(6);

            group!.addShape('text', {
              attrs: {
                x: 0,
                y: -8,
                textBaseline: 'middle',
                textAlign: 'center',
                text: firstLine,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
              },
              name: 'text-shape-1',
            });

            group!.addShape('text', {
              attrs: {
                x: 0,
                y: 8,
                textBaseline: 'middle',
                textAlign: 'center',
                text: secondLine,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
              },
              name: 'text-shape-2',
            });
          } else {
            group!.addShape('text', {
              attrs: {
                textBaseline: 'middle',
                textAlign: 'center',
                text: cfg.label,
                fill: '#000',
                fontSize,
                fontWeight: 'bold',
              },
              name: 'text-shape',
            });
          }
        }

        return rect;
      },
    });



    const graph = new G6.Graph({
      container: containerRef.current,
      width,
      height,
      layout: {
        type: 'dagre',
        rankdir: 'TB',
        align: 'UL',
        nodesep: 50,
        ranksep: 80,
      },
      defaultNode: {
        type: 'component-node',
        size: [100, 40],
      },
      defaultEdge: {
        type: 'polyline',
        style: {
          radius: 10,
          offset: 15,
          endArrow: true,
          lineWidth: 2,
          stroke: '#666',
        },
      },
      modes: {
        default: [
          'drag-canvas',
          'zoom-canvas',
          'drag-node',
        ],
      },
      nodeStateStyles: {
        hover: {
          fill: '#d3f261',
          stroke: '#389e0d',
          lineWidth: 3,
        },
      },
      edgeStateStyles: {
        hover: {
          stroke: '#389e0d',
          lineWidth: 3,
        },
      },
    });

    // 绑定事件
    graph.on('node:click', (evt) => {
      const { item } = evt;
      if (item && onNodeClick) {
        onNodeClick(item.getModel());
      }
    });

    graph.on('edge:click', (evt) => {
      const { item } = evt;
      if (item && onEdgeClick) {
        onEdgeClick(item.getModel());
      }
    });

    graph.on('node:mouseenter', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', true);
        // 设置鼠标为手型
        graph.get('canvas').get('el').style.cursor = 'pointer';
      }
    });

    graph.on('node:mouseleave', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', false);
        // 恢复默认鼠标样式
        graph.get('canvas').get('el').style.cursor = 'default';
      }
    });

    graph.on('edge:mouseenter', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', true);
        // 设置鼠标为手型
        graph.get('canvas').get('el').style.cursor = 'pointer';
      }
    });

    graph.on('edge:mouseleave', (evt) => {
      const { item } = evt;
      if (item) {
        graph.setItemState(item, 'hover', false);
        // 恢复默认鼠标样式
        graph.get('canvas').get('el').style.cursor = 'default';
      }
    });

    graphRef.current = graph;
    return graph;
  };

  // 加载数据
  const loadData = async () => {
    setLoading(true);
    setIsEmpty(false);
    try {
      const rawData = await fetchData();
      const g6Data = transformDataToG6Format(rawData);

      // 检查数据是否为空
      const hasData = g6Data.nodes && g6Data.nodes.length > 0;
      setIsEmpty(!hasData);

      if (graphRef.current) {
        if (hasData) {
          graphRef.current.data(g6Data);
          graphRef.current.render();
          graphRef.current.fitView();
        } else {
          // 清空画布
          graphRef.current.data({ nodes: [], edges: [] });
          graphRef.current.render();
        }
      }
    } catch (error) {
      message.error('加载数据失败');
      console.error('Failed to load canvas data:', error);
      setIsEmpty(true);
    } finally {
      setLoading(false);
    }
  };

  // 组件挂载时初始化图实例（只初始化一次）
  useEffect(() => {
    if (!isInitialized) {
      const graph = initGraph();
      if (graph) {
        setIsInitialized(true);
      }
    }

    // 清理函数
    return () => {
      if (graphRef.current) {
        graphRef.current.destroy();
        graphRef.current = null;
        setIsInitialized(false);
      }
    };
  }, []);

  // 当dataType变化时，只重新加载数据，不重新初始化图实例
  useEffect(() => {
    if (isInitialized && graphRef.current) {
      loadData();
    }
  }, [dataType, isInitialized]);

  // 窗口大小变化时重新调整
  useEffect(() => {
    const handleResize = () => {
      if (graphRef.current && containerRef.current) {
        const width = containerRef.current.offsetWidth;
        graphRef.current.changeSize(width, 600);
        graphRef.current.fitView();
      }
    };

    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  return (
    <div style={{ position: 'relative', width: '100%', height: '600px' }}>
      <Spin spinning={loading}>
        <div
          ref={containerRef}
          style={{
            width: '100%',
            height: '600px',
            border: '1px solid #d9d9d9',
            borderRadius: '6px',
            backgroundColor: '#fafafa',
            position: 'relative',
          }}
        >
          {/* 空数据提示 */}
          {isEmpty && !loading && (
            <div
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)',
                textAlign: 'center',
                zIndex: 10,
              }}
            >
              <Empty
                image={Empty.PRESENTED_IMAGE_SIMPLE}
                description={
                  <span style={{ color: '#999', fontSize: '14px' }}>
                    暂无{dataType === 'dimension' ? '维度' : '指标'}关系数据
                    <br />
                    <span style={{ fontSize: '12px', color: '#ccc' }}>
                      请检查当前Agent是否配置了相关数据
                    </span>
                  </span>
                }
              />
            </div>
          )}
        </div>
      </Spin>
    </div>
  );
};

export default CanvasRenderer;
